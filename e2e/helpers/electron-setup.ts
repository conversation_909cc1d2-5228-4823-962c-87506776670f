import type { Page, ElectronApplication } from 'playwright';
import { setupObsidianElectron, verifyPluginAvailable, resetObsidianUI } from './plugin-setup';

/**
 * Shared Electron Test Setup Utility
 * 
 * This module provides reusable setup functions for Playwright/Electron tests,
 * ensuring consistent test environment initialization across all test suites.
 */

export interface ElectronTestContext {
  electronApp: ElectronApplication;
  page: Page;
}

/**
 * Standard setup for Playwright/Electron tests
 * This is the main function that test suites should use in their beforeAll hooks
 */
export async function setupElectronTest(): Promise<ElectronTestContext> {
  console.log("🔧 Setting up Electron test environment...");
  
  // Launch Obsidian with Playwright/Electron
  const { electronApp, page } = await setupObsidianElectron();
  
  console.log("✅ Electron test environment ready");
  
  return { electronApp, page };
}

/**
 * Standard cleanup for Playwright/Electron tests
 * This should be used in afterAll hooks
 */
export async function cleanupElectronTest(context: ElectronTestContext): Promise<void> {
  if (context.electronApp) {
    try {
      console.log("🔄 Cleaning up Electron test environment...");
      await context.electronApp.close();
      console.log("✅ Electron test environment cleaned up");
    } catch (error) {
      console.log(`⚠️ Electron cleanup failed: ${error.message}`);
    }
  }
}

/**
 * Reset UI state between tests
 * This should be used in beforeEach or afterEach hooks
 */
export async function resetElectronTestUI(page: Page): Promise<void> {
  try {
    await resetObsidianUI(page);
  } catch (error) {
    console.log(`⚠️ UI reset failed: ${error.message}`);
  }
}

/**
 * Helper to create a test file using Obsidian's vault API
 */
export async function createTestFile(page: Page, filePath: string, content: string): Promise<void> {
  await page.evaluate(async ({ path, fileContent }) => {
    try {
      // Create the file using Obsidian's vault API
      await (window as any).app.vault.create(path, fileContent);
      return true;
    } catch (error) {
      // If file already exists, modify it instead
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (file) {
        await (window as any).app.vault.modify(file, fileContent);
        return true;
      }
      throw error;
    }
  }, { path: filePath, fileContent: content });
}

/**
 * Helper to open a file in Obsidian
 */
export async function openFile(page: Page, filePath: string): Promise<void> {
  await page.evaluate(async ({ path }) => {
    const file = (window as any).app.vault.getAbstractFileByPath(path);
    if (!file) {
      throw new Error(`File not found: ${path}`);
    }
    await (window as any).app.workspace.getLeaf().openFile(file);
  }, { path: filePath });
}

/**
 * Helper to wait for async operations
 */
export async function waitForOperation(timeout: number = 1000): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, timeout));
}

/**
 * Complete test setup with all common utilities
 * Use this for a full-featured test environment
 */
export async function setupCompleteElectronTest(): Promise<ElectronTestContext & {
  createFile: (path: string, content: string) => Promise<void>;
  openFile: (path: string) => Promise<void>;
  wait: (timeout?: number) => Promise<void>;
  resetUI: () => Promise<void>;
}> {
  const context = await setupElectronTest();
  
  return {
    ...context,
    createFile: (path: string, content: string) => createTestFile(context.page, path, content),
    openFile: (path: string) => openFile(context.page, path),
    wait: (timeout?: number) => waitForOperation(timeout),
    resetUI: () => resetElectronTestUI(context.page)
  };
}
